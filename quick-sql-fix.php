<?php
/**
 * Quick SQL Fix
 * This script provides immediate fixes for the most common SQL syntax errors
 */

echo "<h1>Quick SQL Syntax Fix</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #f0f8f0; padding: 10px; margin: 10px 0; border-left: 4px solid green; }
    .error { color: red; background: #fff0f0; padding: 10px; margin: 10px 0; border-left: 4px solid red; }
    .warning { color: orange; background: #fff8f0; padding: 10px; margin: 10px 0; border-left: 4px solid orange; }
    .info { color: blue; background: #f0f0ff; padding: 10px; margin: 10px 0; border-left: 4px solid blue; }
    .fix { color: purple; background: #f8f0ff; padding: 10px; margin: 10px 0; border-left: 4px solid purple; }
    pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
    .code-block { background: #f9f9f9; padding: 15px; border: 1px solid #ddd; margin: 10px 0; }
</style>";

echo "<h2>Common SQL Syntax Error: 'current_time'</h2>";
echo "<div class='error'>Error: <code>SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'current_time' at line 1</code></div>";

echo "<h2>Root Cause Analysis</h2>";
echo "<div class='info'>This error typically occurs when:</div>";
echo "<ul>";
echo "<li>Using <code>current_time</code> without parentheses in SQL queries</li>";
echo "<li>MySQL expects <code>CURRENT_TIME()</code> or <code>NOW()</code> for time functions</li>";
echo "<li>A malformed query is being executed somewhere in the application</li>";
echo "</ul>";

echo "<h2>Immediate Fix Solutions</h2>";

echo "<div class='fix'>";
echo "<h3>Solution 1: Replace malformed time functions</h3>";
echo "<div class='code-block'>";
echo "<strong>❌ Incorrect:</strong><br>";
echo "<code>SELECT current_time</code><br>";
echo "<code>SELECT current_date</code><br>";
echo "<code>SELECT current_timestamp</code><br><br>";
echo "<strong>✅ Correct:</strong><br>";
echo "<code>SELECT NOW() as current_time</code><br>";
echo "<code>SELECT CURRENT_DATE() as current_date</code><br>";
echo "<code>SELECT CURRENT_TIMESTAMP as current_timestamp</code><br>";
echo "</div>";
echo "</div>";

echo "<div class='fix'>";
echo "<h3>Solution 2: Check common files for SQL errors</h3>";
echo "<div class='info'>Files most likely to contain the problematic query:</div>";
echo "<ul>";
echo "<li><code>config/config.php</code></li>";
echo "<li><code>config/database.php</code></li>";
echo "<li><code>index.php</code></li>";
echo "<li><code>admin/index.php</code></li>";
echo "<li>Any file that executes SQL queries on page load</li>";
echo "</ul>";
echo "</div>";

// Test the current database connection
echo "<h2>Testing Current Database Setup</h2>";

try {
    // Test 1: Direct connection
    echo "<div class='info'>Test 1: Direct PDO Connection</div>";
    $pdo = new PDO("mysql:host=localhost;dbname=flori_construction;charset=utf8mb4", "root", "", [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    echo "<div class='success'>✅ Direct connection works</div>";
    
    // Test 2: Proper time query
    echo "<div class='info'>Test 2: Proper Time Query</div>";
    $result = $pdo->query("SELECT NOW() as current_time")->fetch();
    echo "<div class='success'>✅ Proper time query works: " . $result['current_time'] . "</div>";
    
    // Test 3: Test problematic query (this should fail)
    echo "<div class='info'>Test 3: Testing Problematic Query (should fail)</div>";
    try {
        $badResult = $pdo->query("SELECT current_time")->fetch();
        echo "<div class='warning'>⚠️ Problematic query unexpectedly worked</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Problematic query failed as expected: " . $e->getMessage() . "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database connection failed: " . $e->getMessage() . "</div>";
}

// Test config loading
echo "<h2>Testing Config File Loading</h2>";

try {
    echo "<div class='info'>Testing config file loading...</div>";
    
    // Capture any errors during config loading
    ob_start();
    $errorBefore = error_get_last();
    
    require_once 'config/config.php';
    
    $output = ob_get_clean();
    $errorAfter = error_get_last();
    
    if ($errorAfter && $errorAfter !== $errorBefore) {
        echo "<div class='error'>❌ Error during config loading:</div>";
        echo "<pre>" . htmlspecialchars(print_r($errorAfter, true)) . "</pre>";
    } else {
        echo "<div class='success'>✅ Config loaded without errors</div>";
    }
    
    if ($output) {
        echo "<div class='warning'>Output during config loading:</div>";
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
    }
    
    // Test the global $db object
    if (isset($db)) {
        echo "<div class='success'>✅ Global \$db object created</div>";
        
        try {
            $result = $db->fetchOne("SELECT NOW() as current_time");
            echo "<div class='success'>✅ Global \$db query works: " . $result['current_time'] . "</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ Global \$db query failed: " . $e->getMessage() . "</div>";
            echo "<div class='warning'>This is likely where your error is occurring!</div>";
        }
    } else {
        echo "<div class='error'>❌ Global \$db object not created</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Config loading failed: " . $e->getMessage() . "</div>";
    echo "<div class='warning'>The error is likely in the config file or its dependencies</div>";
}

echo "<h2>Quick Fix Actions</h2>";

echo "<div class='fix'>";
echo "<h3>Action 1: Clear Cache and Restart</h3>";
echo "<ol>";
echo "<li>Clear your browser cache</li>";
echo "<li>Restart XAMPP/Apache</li>";
echo "<li>Clear any PHP OPcache if enabled</li>";
echo "</ol>";
echo "</div>";

echo "<div class='fix'>";
echo "<h3>Action 2: Check for Malformed Queries</h3>";
echo "<div class='info'>Search your codebase for these patterns and fix them:</div>";
echo "<div class='code-block'>";
echo "<strong>Search for:</strong> <code>current_time</code> (without parentheses)<br>";
echo "<strong>Replace with:</strong> <code>NOW()</code> or <code>CURRENT_TIMESTAMP</code><br><br>";
echo "<strong>Search for:</strong> <code>SELECT current_time</code><br>";
echo "<strong>Replace with:</strong> <code>SELECT NOW() as current_time</code><br>";
echo "</div>";
echo "</div>";

echo "<div class='fix'>";
echo "<h3>Action 3: Temporary Workaround</h3>";
echo "<div class='info'>If you need a quick temporary fix, you can:</div>";
echo "<ol>";
echo "<li>Comment out any problematic SQL queries</li>";
echo "<li>Replace them with hardcoded values temporarily</li>";
echo "<li>Fix the syntax properly later</li>";
echo "</ol>";
echo "</div>";

echo "<h2>Prevention</h2>";
echo "<div class='info'>To prevent this error in the future:</div>";
echo "<ul>";
echo "<li>Always use proper MySQL function syntax: <code>NOW()</code>, <code>CURRENT_TIMESTAMP</code></li>";
echo "<li>Test SQL queries in a MySQL client before using them in PHP</li>";
echo "<li>Use prepared statements for dynamic queries</li>";
echo "<li>Enable error reporting during development</li>";
echo "</ul>";

echo "<div class='success'>";
echo "<h3>✅ Summary</h3>";
echo "<p>The error is caused by malformed SQL syntax using <code>current_time</code> without proper MySQL function syntax.</p>";
echo "<p>Fix by replacing <code>current_time</code> with <code>NOW()</code> or <code>CURRENT_TIMESTAMP</code> in your SQL queries.</p>";
echo "</div>";
?>
