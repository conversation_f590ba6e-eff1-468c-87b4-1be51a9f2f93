<?php
/**
 * Debug SQL Error Script
 * This script will help identify the source of the SQL syntax error
 */

echo "<h1>SQL Error Debug Script</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #f0f8f0; padding: 10px; margin: 10px 0; border-left: 4px solid green; }
    .error { color: red; background: #fff0f0; padding: 10px; margin: 10px 0; border-left: 4px solid red; }
    .warning { color: orange; background: #fff8f0; padding: 10px; margin: 10px 0; border-left: 4px solid orange; }
    .info { color: blue; background: #f0f0ff; padding: 10px; margin: 10px 0; border-left: 4px solid blue; }
    pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
</style>";

// Test 1: Basic PHP connection without config
echo "<h2>Test 1: Direct Database Connection</h2>";
try {
    $host = 'localhost';
    $dbname = 'flori_construction';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "<div class='success'>✅ Direct PDO connection successful</div>";
    
    // Test NOW() function
    $result = $pdo->query("SELECT NOW() as current_time")->fetch();
    echo "<div class='success'>✅ NOW() function works: " . $result['current_time'] . "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Direct connection failed: " . $e->getMessage() . "</div>";
}

// Test 2: Config file loading
echo "<h2>Test 2: Config File Loading</h2>";
try {
    if (file_exists('config/config.php')) {
        echo "<div class='info'>Config file exists, attempting to load...</div>";
        
        // Capture any output/errors during config loading
        ob_start();
        $error_before = error_get_last();
        
        require_once 'config/config.php';
        
        $output = ob_get_clean();
        $error_after = error_get_last();
        
        if ($output) {
            echo "<div class='warning'>Output during config load:</div>";
            echo "<pre>" . htmlspecialchars($output) . "</pre>";
        }
        
        if ($error_after && $error_after !== $error_before) {
            echo "<div class='error'>Error during config load:</div>";
            echo "<pre>" . htmlspecialchars(print_r($error_after, true)) . "</pre>";
        }
        
        echo "<div class='success'>✅ Config file loaded</div>";
        
        if (isset($db)) {
            echo "<div class='success'>✅ Database object created</div>";
            
            // Test the database object
            try {
                $result = $db->fetchOne("SELECT NOW() as current_time");
                echo "<div class='success'>✅ Database object query successful: " . $result['current_time'] . "</div>";
            } catch (Exception $e) {
                echo "<div class='error'>❌ Database object query failed: " . $e->getMessage() . "</div>";
                
                // Try to get more details about the error
                echo "<div class='info'>Attempting to debug the database class...</div>";
                
                // Check if the database class has any issues
                try {
                    $connection = $db->getConnection();
                    echo "<div class='success'>✅ Database connection object accessible</div>";
                    
                    // Try a direct query on the connection
                    $stmt = $connection->query("SELECT NOW() as current_time");
                    $result = $stmt->fetch();
                    echo "<div class='success'>✅ Direct connection query works: " . $result['current_time'] . "</div>";
                    
                } catch (Exception $e2) {
                    echo "<div class='error'>❌ Direct connection query failed: " . $e2->getMessage() . "</div>";
                }
            }
        } else {
            echo "<div class='error'>❌ Database object not created</div>";
        }
        
    } else {
        echo "<div class='error'>❌ Config file not found</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Config loading failed: " . $e->getMessage() . "</div>";
    echo "<div class='info'>Error details:</div>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

// Test 3: Check for any auto-loaded files or scripts
echo "<h2>Test 3: Check for Auto-loaded Scripts</h2>";

$autoloadFiles = [
    'config/database.php',
    'config/config.php'
];

foreach ($autoloadFiles as $file) {
    if (file_exists($file)) {
        echo "<div class='info'>Checking file: $file</div>";
        $content = file_get_contents($file);
        
        // Look for any suspicious SQL queries
        if (preg_match('/current_time(?!\s*\()/i', $content)) {
            echo "<div class='warning'>⚠️ Found 'current_time' without parentheses in $file</div>";
        }
        
        if (preg_match('/SELECT\s+current_time(?!\s*\()/i', $content)) {
            echo "<div class='error'>❌ Found malformed 'SELECT current_time' in $file</div>";
        }
        
        echo "<div class='success'>✅ File $file appears clean</div>";
    }
}

// Test 4: Check recent error logs
echo "<h2>Test 4: Recent Error Information</h2>";
$lastError = error_get_last();
if ($lastError) {
    echo "<div class='info'>Last PHP error:</div>";
    echo "<pre>" . htmlspecialchars(print_r($lastError, true)) . "</pre>";
} else {
    echo "<div class='success'>✅ No recent PHP errors</div>";
}

echo "<h2>Debug Complete</h2>";
echo "<div class='info'>If you're still seeing the 'current_time' error, it might be coming from:</div>";
echo "<ul>";
echo "<li>A cached file or session</li>";
echo "<li>A file that's being included automatically</li>";
echo "<li>A database trigger or stored procedure</li>";
echo "<li>Another script running in the background</li>";
echo "</ul>";
?>
