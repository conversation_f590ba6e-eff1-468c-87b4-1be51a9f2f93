<?php
/**
 * Fix SQL Syntax Error - current_time issue
 * This script will identify and fix the SQL syntax error related to 'current_time'
 */

echo "<h1>SQL Syntax Error Fix</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #f0f8f0; padding: 10px; margin: 10px 0; border-left: 4px solid green; }
    .error { color: red; background: #fff0f0; padding: 10px; margin: 10px 0; border-left: 4px solid red; }
    .warning { color: orange; background: #fff8f0; padding: 10px; margin: 10px 0; border-left: 4px solid orange; }
    .info { color: blue; background: #f0f0ff; padding: 10px; margin: 10px 0; border-left: 4px solid blue; }
    .fix { color: purple; background: #f8f0ff; padding: 10px; margin: 10px 0; border-left: 4px solid purple; }
    pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
</style>";

// Function to scan and fix SQL syntax issues
function scanAndFixSQLSyntax($directory = '.') {
    $fixes = [];
    $problematicPatterns = [
        // Pattern 1: SELECT current_time (without parentheses)
        '/SELECT\s+current_time(?!\s*\()/i' => 'SELECT CURRENT_TIMESTAMP',
        // Pattern 2: current_time in WHERE clauses (without parentheses)
        '/WHERE\s+.*?current_time(?!\s*\()/i' => 'WHERE ... CURRENT_TIMESTAMP',
        // Pattern 3: current_time in INSERT/UPDATE (without parentheses)
        '/(?:INSERT|UPDATE).*?current_time(?!\s*\()/i' => 'Use CURRENT_TIMESTAMP or NOW()',
        // Pattern 4: Standalone current_time
        '/\bcurrent_time\b(?!\s*\()/i' => 'CURRENT_TIMESTAMP'
    ];
    
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($directory));
    
    foreach ($iterator as $file) {
        if ($file->isFile() && $file->getExtension() === 'php') {
            $filePath = $file->getPathname();
            $content = file_get_contents($filePath);
            $originalContent = $content;
            $fileFixed = false;
            
            foreach ($problematicPatterns as $pattern => $replacement) {
                if (preg_match($pattern, $content)) {
                    $fixes[] = [
                        'file' => $filePath,
                        'pattern' => $pattern,
                        'replacement' => $replacement,
                        'matches' => []
                    ];
                    
                    // Get specific matches for reporting
                    preg_match_all($pattern, $content, $matches, PREG_OFFSET_CAPTURE);
                    $fixes[count($fixes)-1]['matches'] = $matches[0];
                    
                    $fileFixed = true;
                }
            }
            
            if ($fileFixed) {
                $fixes[] = [
                    'file' => $filePath,
                    'needs_manual_review' => true,
                    'content_preview' => substr($content, 0, 500) . '...'
                ];
            }
        }
    }
    
    return $fixes;
}

echo "<h2>Scanning for SQL Syntax Issues</h2>";

$fixes = scanAndFixSQLSyntax();

if (empty($fixes)) {
    echo "<div class='success'>✅ No obvious SQL syntax issues found in PHP files</div>";
} else {
    echo "<div class='warning'>⚠️ Found " . count($fixes) . " potential SQL syntax issues:</div>";
    
    foreach ($fixes as $fix) {
        if (isset($fix['needs_manual_review'])) {
            continue; // Skip summary entries
        }
        
        echo "<div class='error'>";
        echo "<strong>File:</strong> " . htmlspecialchars($fix['file']) . "<br>";
        echo "<strong>Pattern:</strong> " . htmlspecialchars($fix['pattern']) . "<br>";
        echo "<strong>Suggested Fix:</strong> " . htmlspecialchars($fix['replacement']) . "<br>";
        
        if (!empty($fix['matches'])) {
            echo "<strong>Matches found:</strong><br>";
            foreach ($fix['matches'] as $match) {
                echo "<code>" . htmlspecialchars($match[0]) . "</code> at position " . $match[1] . "<br>";
            }
        }
        echo "</div>";
    }
}

// Test database connection with proper syntax
echo "<h2>Testing Database Connection with Proper SQL Syntax</h2>";

try {
    $pdo = new PDO("mysql:host=localhost;dbname=flori_construction;charset=utf8mb4", "root", "", [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "<div class='success'>✅ Database connection successful</div>";
    
    // Test various time functions with proper syntax
    $timeTests = [
        "SELECT NOW() as current_time",
        "SELECT CURRENT_TIMESTAMP as current_timestamp",
        "SELECT CURRENT_DATE() as current_date",
        "SELECT UNIX_TIMESTAMP() as unix_timestamp"
    ];
    
    foreach ($timeTests as $query) {
        try {
            $result = $pdo->query($query)->fetch();
            echo "<div class='success'>✅ Query works: <code>" . htmlspecialchars($query) . "</code></div>";
            echo "<div class='info'>Result: " . htmlspecialchars(print_r($result, true)) . "</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ Query failed: <code>" . htmlspecialchars($query) . "</code></div>";
            echo "<div class='error'>Error: " . $e->getMessage() . "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database connection failed: " . $e->getMessage() . "</div>";
}

echo "<h2>Manual Fix Instructions</h2>";
echo "<div class='info'>If you found SQL syntax issues above, here's how to fix them:</div>";
echo "<ol>";
echo "<li><strong>current_time without parentheses:</strong> Replace with <code>CURRENT_TIMESTAMP</code> or <code>NOW()</code></li>";
echo "<li><strong>current_date without parentheses:</strong> Replace with <code>CURRENT_DATE()</code></li>";
echo "<li><strong>Malformed SELECT statements:</strong> Ensure proper MySQL function syntax</li>";
echo "<li><strong>Check for typos:</strong> Look for missing commas, quotes, or parentheses</li>";
echo "</ol>";

echo "<div class='warning'>Common MySQL time functions (correct syntax):</div>";
echo "<ul>";
echo "<li><code>NOW()</code> - Current date and time</li>";
echo "<li><code>CURRENT_TIMESTAMP</code> - Current timestamp (same as NOW())</li>";
echo "<li><code>CURRENT_DATE()</code> - Current date only</li>";
echo "<li><code>CURRENT_TIME()</code> - Current time only</li>";
echo "<li><code>UNIX_TIMESTAMP()</code> - Unix timestamp</li>";
echo "</ul>";

echo "<h2>Next Steps</h2>";
echo "<div class='info'>To resolve the SQL syntax error:</div>";
echo "<ol>";
echo "<li>Review the files identified above for SQL syntax issues</li>";
echo "<li>Replace any malformed <code>current_time</code> usage with proper MySQL functions</li>";
echo "<li>Test your application after making changes</li>";
echo "<li>Clear any cached files or restart your web server</li>";
echo "</ol>";
?>
