<?php
/**
 * Mobile App Database Setup and Verification
 * Ensures all required tables and data exist for the mobile app
 */

echo "<!DOCTYPE html>";
echo "<html><head><title>Mobile App Setup</title>";
echo "<style>
    body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style></head><body>";

echo "<h1>🔧 Mobile App Setup & Database Verification</h1>";

try {
    // Test database connection
    echo "<div class='section'>";
    echo "<h2>📊 Database Connection Test</h2>";
    
    $host = 'localhost';
    $dbname = 'flori_construction';
    $username = 'root';
    $password = '';
    
    // First, connect without specifying database
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "<p class='success'>✅ MySQL connection successful</p>";
    
    // Check if database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE 'flori_construction'");
    $dbExists = $stmt->fetch();
    
    if (!$dbExists) {
        echo "<p class='warning'>⚠️ Database 'flori_construction' doesn't exist. Creating...</p>";
        $pdo->exec("CREATE DATABASE flori_construction");
        echo "<p class='success'>✅ Database created</p>";
    } else {
        echo "<p class='success'>✅ Database 'flori_construction' exists</p>";
    }
    
    // Connect to the database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "</div>";
    
    // Check and create required tables
    echo "<div class='section'>";
    echo "<h2>📋 Required Tables Check</h2>";
    
    $requiredTables = [
        'users' => "CREATE TABLE users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            role ENUM('admin', 'editor') DEFAULT 'editor',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            last_login TIMESTAMP NULL,
            is_active BOOLEAN DEFAULT TRUE
        )",
        'projects' => "CREATE TABLE projects (
            id INT PRIMARY KEY AUTO_INCREMENT,
            title VARCHAR(150) NOT NULL,
            slug VARCHAR(150) UNIQUE NOT NULL,
            description TEXT,
            short_description VARCHAR(255),
            client_name VARCHAR(100),
            location VARCHAR(150),
            project_type ENUM('completed', 'ongoing') NOT NULL,
            start_date DATE,
            end_date DATE,
            featured_image VARCHAR(255),
            gallery JSON,
            services JSON,
            project_value DECIMAL(12,2),
            is_featured BOOLEAN DEFAULT FALSE,
            sort_order INT DEFAULT 0,
            meta_title VARCHAR(100),
            meta_description VARCHAR(160),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        'api_tokens' => "CREATE TABLE api_tokens (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            token VARCHAR(255) UNIQUE NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )",
        'media' => "CREATE TABLE media (
            id INT PRIMARY KEY AUTO_INCREMENT,
            filename VARCHAR(255) NOT NULL,
            original_name VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_type VARCHAR(50) NOT NULL,
            file_size INT NOT NULL,
            mime_type VARCHAR(100) NOT NULL,
            alt_text VARCHAR(255),
            caption TEXT,
            uploaded_by INT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL
        )"
    ];
    
    foreach ($requiredTables as $tableName => $createSQL) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
        $tableExists = $stmt->fetch();
        
        if (!$tableExists) {
            echo "<p class='warning'>⚠️ Table '$tableName' missing. Creating...</p>";
            $pdo->exec($createSQL);
            echo "<p class='success'>✅ Table '$tableName' created</p>";
        } else {
            echo "<p class='success'>✅ Table '$tableName' exists</p>";
            
            // Show record count
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $tableName");
            $count = $stmt->fetch()['count'];
            echo "<p style='margin-left: 20px;'>Records: $count</p>";
        }
    }
    
    echo "</div>";
    
    // Check and create default admin user
    echo "<div class='section'>";
    echo "<h2>👤 Default Admin User</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE username = 'admin'");
    $adminExists = $stmt->fetch()['count'] > 0;
    
    if (!$adminExists) {
        echo "<p class='warning'>⚠️ Default admin user doesn't exist. Creating...</p>";
        
        $adminData = [
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password_hash' => password_hash('admin123', PASSWORD_DEFAULT),
            'full_name' => 'Administrator',
            'role' => 'admin',
            'is_active' => 1
        ];
        
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password_hash, full_name, role, is_active) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(array_values($adminData));
        
        echo "<p class='success'>✅ Default admin user created</p>";
        echo "<p class='info'>📝 Username: admin</p>";
        echo "<p class='info'>📝 Password: admin123</p>";
        echo "<p class='warning'>⚠️ Please change the default password after login!</p>";
    } else {
        echo "<p class='success'>✅ Admin user exists</p>";
        
        // Show admin user details
        $stmt = $pdo->query("SELECT username, email, full_name, role, is_active FROM users WHERE username = 'admin'");
        $admin = $stmt->fetch();
        echo "<p style='margin-left: 20px;'>Username: {$admin['username']}</p>";
        echo "<p style='margin-left: 20px;'>Email: {$admin['email']}</p>";
        echo "<p style='margin-left: 20px;'>Role: {$admin['role']}</p>";
        echo "<p style='margin-left: 20px;'>Active: " . ($admin['is_active'] ? 'Yes' : 'No') . "</p>";
    }
    
    echo "</div>";
    
    // Test API endpoints
    echo "<div class='section'>";
    echo "<h2>🔌 API Endpoint Tests</h2>";
    
    $apiTests = [
        'auth.php?action=ping' => 'Authentication API',
        'mobile.php?action=ping' => 'Mobile API'
    ];
    
    foreach ($apiTests as $endpoint => $description) {
        $url = "http://localhost:8080/api/$endpoint";
        echo "<p>Testing $description: <code>$endpoint</code></p>";
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 5,
                'ignore_errors' => true
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response !== false) {
            $data = json_decode($response, true);
            if ($data && isset($data['success']) && $data['success']) {
                echo "<p class='success' style='margin-left: 20px;'>✅ $description working</p>";
            } else {
                echo "<p class='warning' style='margin-left: 20px;'>⚠️ $description responded but with error</p>";
                echo "<pre style='margin-left: 20px;'>" . htmlspecialchars($response) . "</pre>";
            }
        } else {
            echo "<p class='error' style='margin-left: 20px;'>❌ $description not accessible</p>";
        }
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>✅ Setup Complete</h2>";
    echo "<p>Your mobile app database is now ready!</p>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ol>";
    echo "<li><a href='mobile-app/index.html'>Open Mobile App</a></li>";
    echo "<li><a href='mobile-app/test-projects-fixed.html'>Test Mobile App Functionality</a></li>";
    echo "<li>Login with username: <code>admin</code> and password: <code>admin123</code></li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<h2 class='error'>❌ Setup Error</h2>";
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check:</p>";
    echo "<ul>";
    echo "<li>XAMPP is running</li>";
    echo "<li>MySQL service is started</li>";
    echo "<li>Database credentials are correct</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</body></html>";
?>
