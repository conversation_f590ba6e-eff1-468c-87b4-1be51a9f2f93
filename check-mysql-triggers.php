<?php
/**
 * Check MySQL Triggers and Procedures
 * This script will check for any database triggers or stored procedures that might contain the problematic SQL
 */

echo "<h1>MySQL Triggers and Procedures Check</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #f0f8f0; padding: 10px; margin: 10px 0; border-left: 4px solid green; }
    .error { color: red; background: #fff0f0; padding: 10px; margin: 10px 0; border-left: 4px solid red; }
    .warning { color: orange; background: #fff8f0; padding: 10px; margin: 10px 0; border-left: 4px solid orange; }
    .info { color: blue; background: #f0f0ff; padding: 10px; margin: 10px 0; border-left: 4px solid blue; }
    pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

try {
    // Connect directly to MySQL
    $host = 'localhost';
    $dbname = 'flori_construction';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "<div class='success'>✅ Connected to MySQL database</div>";
    
    // Check for triggers
    echo "<h2>Checking Database Triggers</h2>";
    try {
        $triggers = $pdo->query("SHOW TRIGGERS")->fetchAll();
        
        if (empty($triggers)) {
            echo "<div class='success'>✅ No triggers found in database</div>";
        } else {
            echo "<div class='info'>Found " . count($triggers) . " triggers:</div>";
            echo "<table>";
            echo "<tr><th>Trigger</th><th>Event</th><th>Table</th><th>Statement</th></tr>";
            
            foreach ($triggers as $trigger) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($trigger['Trigger']) . "</td>";
                echo "<td>" . htmlspecialchars($trigger['Event']) . "</td>";
                echo "<td>" . htmlspecialchars($trigger['Table']) . "</td>";
                echo "<td><pre>" . htmlspecialchars($trigger['Statement']) . "</pre></td>";
                echo "</tr>";
                
                // Check if trigger contains problematic SQL
                if (preg_match('/current_time(?!\s*\()/i', $trigger['Statement'])) {
                    echo "<tr><td colspan='4' class='error'>❌ This trigger contains problematic 'current_time' usage!</td></tr>";
                }
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error checking triggers: " . $e->getMessage() . "</div>";
    }
    
    // Check for stored procedures
    echo "<h2>Checking Stored Procedures</h2>";
    try {
        $procedures = $pdo->query("SHOW PROCEDURE STATUS WHERE Db = '$dbname'")->fetchAll();
        
        if (empty($procedures)) {
            echo "<div class='success'>✅ No stored procedures found in database</div>";
        } else {
            echo "<div class='info'>Found " . count($procedures) . " stored procedures:</div>";
            echo "<table>";
            echo "<tr><th>Name</th><th>Type</th><th>Created</th><th>Modified</th></tr>";
            
            foreach ($procedures as $procedure) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($procedure['Name']) . "</td>";
                echo "<td>" . htmlspecialchars($procedure['Type']) . "</td>";
                echo "<td>" . htmlspecialchars($procedure['Created']) . "</td>";
                echo "<td>" . htmlspecialchars($procedure['Modified']) . "</td>";
                echo "</tr>";
                
                // Get procedure definition
                try {
                    $definition = $pdo->query("SHOW CREATE PROCEDURE `{$procedure['Name']}`")->fetch();
                    if ($definition && isset($definition['Create Procedure'])) {
                        echo "<tr><td colspan='4'><pre>" . htmlspecialchars($definition['Create Procedure']) . "</pre></td></tr>";
                        
                        // Check if procedure contains problematic SQL
                        if (preg_match('/current_time(?!\s*\()/i', $definition['Create Procedure'])) {
                            echo "<tr><td colspan='4' class='error'>❌ This procedure contains problematic 'current_time' usage!</td></tr>";
                        }
                    }
                } catch (Exception $e) {
                    echo "<tr><td colspan='4' class='warning'>⚠️ Could not get definition: " . $e->getMessage() . "</td></tr>";
                }
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error checking procedures: " . $e->getMessage() . "</div>";
    }
    
    // Check for functions
    echo "<h2>Checking User-Defined Functions</h2>";
    try {
        $functions = $pdo->query("SHOW FUNCTION STATUS WHERE Db = '$dbname'")->fetchAll();
        
        if (empty($functions)) {
            echo "<div class='success'>✅ No user-defined functions found in database</div>";
        } else {
            echo "<div class='info'>Found " . count($functions) . " functions:</div>";
            echo "<table>";
            echo "<tr><th>Name</th><th>Type</th><th>Created</th><th>Modified</th></tr>";
            
            foreach ($functions as $function) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($function['Name']) . "</td>";
                echo "<td>" . htmlspecialchars($function['Type']) . "</td>";
                echo "<td>" . htmlspecialchars($function['Created']) . "</td>";
                echo "<td>" . htmlspecialchars($function['Modified']) . "</td>";
                echo "</tr>";
                
                // Get function definition
                try {
                    $definition = $pdo->query("SHOW CREATE FUNCTION `{$function['Name']}`")->fetch();
                    if ($definition && isset($definition['Create Function'])) {
                        echo "<tr><td colspan='4'><pre>" . htmlspecialchars($definition['Create Function']) . "</pre></td></tr>";
                        
                        // Check if function contains problematic SQL
                        if (preg_match('/current_time(?!\s*\()/i', $definition['Create Function'])) {
                            echo "<tr><td colspan='4' class='error'>❌ This function contains problematic 'current_time' usage!</td></tr>";
                        }
                    }
                } catch (Exception $e) {
                    echo "<tr><td colspan='4' class='warning'>⚠️ Could not get definition: " . $e->getMessage() . "</td></tr>";
                }
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error checking functions: " . $e->getMessage() . "</div>";
    }
    
    // Check for views
    echo "<h2>Checking Database Views</h2>";
    try {
        $views = $pdo->query("SHOW FULL TABLES WHERE Table_type = 'VIEW'")->fetchAll();
        
        if (empty($views)) {
            echo "<div class='success'>✅ No views found in database</div>";
        } else {
            echo "<div class='info'>Found " . count($views) . " views:</div>";
            echo "<table>";
            echo "<tr><th>View Name</th><th>Definition</th></tr>";
            
            foreach ($views as $view) {
                $viewName = $view['Tables_in_' . $dbname];
                echo "<tr>";
                echo "<td>" . htmlspecialchars($viewName) . "</td>";
                
                try {
                    $definition = $pdo->query("SHOW CREATE VIEW `$viewName`")->fetch();
                    if ($definition && isset($definition['Create View'])) {
                        echo "<td><pre>" . htmlspecialchars($definition['Create View']) . "</pre></td>";
                        
                        // Check if view contains problematic SQL
                        if (preg_match('/current_time(?!\s*\()/i', $definition['Create View'])) {
                            echo "</tr><tr><td colspan='2' class='error'>❌ This view contains problematic 'current_time' usage!</td>";
                        }
                    } else {
                        echo "<td>Could not retrieve definition</td>";
                    }
                } catch (Exception $e) {
                    echo "<td class='warning'>Error: " . $e->getMessage() . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error checking views: " . $e->getMessage() . "</div>";
    }
    
    // Test a simple query to see if the error occurs
    echo "<h2>Testing Simple Queries</h2>";
    
    $testQueries = [
        "SELECT NOW() as current_time",
        "SELECT CURRENT_TIMESTAMP as current_time",
        "SELECT 1 as test",
        "SHOW TABLES"
    ];
    
    foreach ($testQueries as $query) {
        try {
            echo "<div class='info'>Testing: <code>" . htmlspecialchars($query) . "</code></div>";
            $result = $pdo->query($query)->fetch();
            echo "<div class='success'>✅ Query executed successfully</div>";
            if ($result) {
                echo "<div class='info'>Result: " . htmlspecialchars(print_r($result, true)) . "</div>";
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ Query failed: " . $e->getMessage() . "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database connection failed: " . $e->getMessage() . "</div>";
}

echo "<h2>Analysis Complete</h2>";
echo "<div class='info'>If no problematic database objects were found, the error is likely in PHP code, not in the database itself.</div>";
?>
